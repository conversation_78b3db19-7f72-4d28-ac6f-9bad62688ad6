// app/providers.tsx
'use client'

import { useEffect } from "react"
import posthog from 'posthog-js'
import { PostHog<PERSON>rovider as PHProvider } from 'posthog-js/react'

export function PostHogProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY as string, {
        api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://eu.i.posthog.com',
        person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
        capture_pageview: false // Disable automatic pageview capture, as we capture manually
      })
    }
  }, [])

  return (
    <PHProvider>
      {children}
    </PHProvider>
  )
}
